uuid: 3ae67cfa-42c0-4d83-8cd6-9cb4d852bdbf
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_featured_video
    - media.type.nexx_video
    - node.type.issue
id: node.issue.field_featured_video
field_name: field_featured_video
entity_type: node
bundle: issue
label: 'Featured Video'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      nexx_video: nexx_video
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: nexx_video
field_type: entity_reference
