uuid: 582482fc-b85d-4eb2-a578-f29a7bb8d005
langcode: en
status: true
dependencies:
  config:
    - user.role.administrator
  module:
    - graphql_views
    - media
    - pb_girl
    - pb_girl_info
    - user
id: graphql_featu
label: 'GraphQL / Featured Girl Infos'
module: views
description: ''
tag: ''
base_table: media_field_data
base_field: mid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        mid:
          id: mid
          table: media_field_data
          field: mid
          relationship: none
          group_type: count
          admin_label: ''
          entity_type: media
          entity_field: mid
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings: {  }
          group_column: entity_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ''
          field_api_classes: false
          set_precision: false
          precision: 0
          decimal: .
          format_plural: 0
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            administrator: administrator
      cache:
        type: time
        options:
          results_lifespan: 3600
          results_lifespan_custom: 0
          output_lifespan: 3600
          output_lifespan_custom: 0
      empty: {  }
      sorts:
        field_publish_date_value:
          id: field_publish_date_value
          table: media__field_publish_date
          field: field_publish_date_value
          relationship: none
          group_type: max
          admin_label: ''
          plugin_id: datetime
          order: DESC
          expose:
            label: 'Publish date (field_publish_date)'
            field_identifier: field_publish_date_value
          exposed: true
          granularity: second
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      arguments: {  }
      filters:
        status:
          id: status
          table: media_field_data
          field: status
          entity_type: media
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        status_1:
          id: status_1
          table: girl_field_data
          field: status
          relationship: girl
          group_type: group
          admin_label: ''
          entity_type: girl
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status_2:
          id: status_2
          table: girl_info_field_data
          field: status
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_featured_value:
          id: field_featured_value
          table: girl_info__field_featured
          field: field_featured_value
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
          3: AND
          4: OR
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        reverse__girl_info__galleries:
          id: reverse__girl_info__galleries
          table: media_field_data
          field: reverse__girl_info__galleries
          relationship: none
          group_type: group
          admin_label: info
          entity_type: media
          plugin_id: entity_reverse
          required: true
        girl:
          id: girl
          table: girl_info_field_data
          field: girl
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: Girl
          entity_type: girl_info
          entity_field: girl
          plugin_id: standard
          required: true
      group_by: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user.roles
      tags: {  }
  graphql_1:
    id: graphql_1
    display_title: GraphQL
    display_plugin: graphql
    position: 1
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      style:
        type: graphql
        options:
          uses_fields: false
      row:
        type: graphql_entity
        options: {  }
      display_extenders: {  }
      graphql_query_name: pbFeatured
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user.roles
      tags: {  }
