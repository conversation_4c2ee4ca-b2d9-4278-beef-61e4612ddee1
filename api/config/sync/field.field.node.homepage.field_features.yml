uuid: 2fb823a5-f20b-4efe-b03b-59d62bea570e
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_features
    - node.type.homepage
    - paragraphs.paragraphs_type.homepage_features
  module:
    - entity_reference_revisions
id: node.homepage.field_features
field_name: field_features
entity_type: node
bundle: homepage
label: Features
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      homepage_features: homepage_features
    negate: 0
    target_bundles_drag_drop:
      homepage_features:
        weight: 3
        enabled: true
      nexx_video:
        weight: 4
        enabled: false
field_type: entity_reference_revisions
