uuid: de8a1102-4b90-4203-837a-3bb8cac1051e
langcode: en
status: true
dependencies: {  }
_core:
  default_config_hash: ESG2x3MSLc7DomtiTVDA4Ye7Bsz0Og_LE1C1h6RQwx0
id: pb_girl_info_gallery
class: null
field_plugin_method: null
cck_plugin_method: null
migration_tags: null
migration_group: pb_girl_info_gallery
label: 'Import girl gallery entities'
source:
  plugin: url
  glob: true
  urls:
    - 'public://migration/girl_info/processed/galleries/*.json'
  data_fetcher_plugin: file
  data_parser_plugin: json
  item_selector: galleries
  fields:
    -
      name: girl_gallery_id
      label: 'Primary girl gallery id'
      selector: id
    -
      name: uuid
      label: Uuid
      selector: uuid
    -
      name: name
      label: name
      selector: title
    -
      name: credit
      label: Credit
      selector: credit
    -
      name: state
      label: State
      selector: state
    -
      name: description
      label: description
      selector: description
    -
      name: tags
      label: Tags
      selector: tags
    -
      name: publish_date
      label: 'Publish date'
      selector: publishedFrom
    -
      name: unpublish_date
      label: 'Unpublish date'
      selector: publishedThru
    -
      name: image
      label: Image
      selector: mainImage/normal/file
    -
      name: images
      label: Images
      selector: images
    -
      name: releasedate
      label: 'Release date'
      selector: released
    -
      name: focal_x
      label: focal_x
      selector: mainImage/normal/focalPoint/x
    -
      name: focal_y
      label: focal_y
      selector: mainImage/normal/focalPoint/y
  ids:
    uuid:
      type: string
  constants:
    file_destination: 'private://'
process:
  uuid: uuid
  field_focal_point_x: focal_x
  field_focal_point_y: focal_y
destination:
  plugin: 'entity:media'
  default_bundle: gallery
migration_dependencies:
  required:
    - pb_girl_image
  optional: {  }
