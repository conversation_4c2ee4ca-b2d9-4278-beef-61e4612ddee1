uuid: 84a37c2b-202d-4a03-aa52-c84e27a65600
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_category
    - taxonomy.vocabulary.category
  module:
    - pb_girl_info
id: girl_info.girl_info.field_category
field_name: field_category
entity_type: girl_info
bundle: girl_info
label: Category
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      category: category
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
