uuid: 1c8c0dec-e2c9-4ff1-ae5d-556deee81269
langcode: en
status: true
dependencies:
  module:
    - file
    - user
_core:
  default_config_hash: hfAVzEhCVBiV_vsgQ0UP4CfSHmh2YSiEUhfRz-LMYAQ
id: files
label: 'Admin Page / Files'
module: file
description: 'Find and manage files.'
tag: default
base_table: file_managed
base_field: fid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: Files
      fields:
        fid:
          id: fid
          table: file_managed
          field: fid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: fid
          plugin_id: field
          label: Fid
          exclude: true
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        filename:
          id: filename
          table: file_managed
          field: filename
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: filename
          plugin_id: field
          label: Name
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: file_link
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        filemime:
          id: filemime
          table: file_managed
          field: filemime
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: filemime
          plugin_id: field
          label: 'MIME type'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: file_filemime
        filesize:
          id: filesize
          table: file_managed
          field: filesize
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: filesize
          plugin_id: field
          label: Size
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: file_size
        status:
          id: status
          table: file_managed
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: status
          plugin_id: field
          label: Status
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: boolean
          settings:
            format: custom
            format_custom_false: Temporary
            format_custom_true: Permanent
        created:
          id: created
          table: file_managed
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: created
          plugin_id: field
          label: 'Upload date'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: timestamp
          settings:
            date_format: medium
            custom_date_format: ''
            timezone: ''
        changed:
          id: changed
          table: file_managed
          field: changed
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: changed
          plugin_id: field
          label: 'Changed date'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: timestamp
          settings:
            date_format: medium
            custom_date_format: ''
            timezone: ''
        count:
          id: count
          table: file_usage
          field: count
          relationship: fid
          group_type: sum
          admin_label: ''
          plugin_id: numeric
          label: 'Used in'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: true
            path: 'admin/content/files/usage/{{ fid }}'
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ','
          format_plural: true
          format_plural_string: !!binary MSBwbGFjZQNAY291bnQgcGxhY2Vz
          prefix: ''
          suffix: ''
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 50
          total_pages: 0
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Filter
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access files overview'
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          plugin_id: text_custom
          empty: true
          content: 'No files available.'
      sorts: {  }
      arguments: {  }
      filters:
        filename:
          id: filename
          table: file_managed
          field: filename
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: filename
          plugin_id: string
          operator: word
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: filemime_op
            label: Filename
            description: ''
            use_operator: false
            operator: filename_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filename
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filemime:
          id: filemime
          table: file_managed
          field: filemime
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: filemime
          plugin_id: string
          operator: word
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: filemime_op
            label: 'MIME type'
            description: ''
            use_operator: false
            operator: filemime_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filemime
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status:
          id: status
          table: file_managed
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: status
          plugin_id: file_status
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: status_op
            label: Status
            description: ''
            use_operator: false
            operator: status_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: status
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            fid: fid
            filename: filename
            filemime: filemime
            filesize: filesize
            status: status
            created: created
            changed: changed
            count: count
          default: changed
          info:
            fid:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            filename:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            filemime:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-medium
            filesize:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-low
            status:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-low
            created:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            changed:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            count:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-medium
          override: true
          sticky: false
          summary: ''
          empty_table: true
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        fid:
          id: fid
          table: file_managed
          field: fid
          relationship: none
          group_type: group
          admin_label: 'File usage'
          required: true
      group_by: true
      show_admin_links: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
  page_1:
    id: page_1
    display_title: 'Files overview'
    display_plugin: page
    position: 1
    display_options:
      defaults:
        pager: true
        relationships: false
      relationships:
        fid:
          id: fid
          table: file_managed
          field: fid
          relationship: none
          group_type: group
          admin_label: 'File usage'
          required: false
      display_description: ''
      display_extenders: {  }
      path: admin/content/files
      menu:
        type: tab
        title: Files
        description: ''
        weight: 0
        menu_name: admin
        context: ''
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
  page_2:
    id: page_2
    display_title: 'File usage'
    display_plugin: page
    position: 2
    display_options:
      title: 'File usage'
      fields:
        entity_label:
          id: entity_label
          table: file_usage
          field: entity_label
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: entity_label
          label: Entity
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          link_to_entity: true
        type:
          id: type
          table: file_usage
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          label: 'Entity type'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        module:
          id: module
          table: file_usage
          field: module
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          label: 'Registering module'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        count:
          id: count
          table: file_usage
          field: count
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: numeric
          label: 'Use count'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ','
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: 0
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      empty: {  }
      arguments:
        fid:
          id: fid
          table: file_managed
          field: fid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: file
          entity_field: fid
          plugin_id: file_fid
          default_action: 'not found'
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: true
          title: 'File usage information for {{ arguments.fid }}'
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          default_argument_skip_url: false
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters: {  }
      filter_groups:
        operator: AND
        groups: {  }
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            entity_label: entity_label
            type: type
            module: module
            count: count
          default: entity_label
          info:
            entity_label:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            type:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-medium
            module:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-low
            count:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: true
          caption: ''
          description: ''
      row:
        type: fields
        options: {  }
      defaults:
        empty: false
        title: false
        pager: false
        group_by: false
        style: false
        row: false
        relationships: false
        fields: false
        arguments: false
        filters: false
        filter_groups: false
      relationships:
        fid:
          id: fid
          table: file_managed
          field: fid
          relationship: none
          group_type: group
          admin_label: 'File usage'
          required: true
      group_by: false
      display_description: ''
      display_extenders: {  }
      path: admin/content/files/usage/%
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
