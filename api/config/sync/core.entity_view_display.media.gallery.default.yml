uuid: 66ee6f0f-fbda-4bdb-a065-7b28117d2063
langcode: en
status: true
dependencies:
  config:
    - field.field.media.gallery.field_credit
    - field.field.media.gallery.field_image
    - field.field.media.gallery.field_media_image
    - field.field.media.gallery.field_media_slideshow
    - field.field.media.gallery.field_publish_date
    - field.field.media.gallery.field_release_date
    - field.field.media.gallery.field_tags
    - field.field.media.gallery.field_unpublish_date
    - field.field.media.gallery.field_videos
    - image.style.large
    - media.type.gallery
  module:
    - datetime
    - image
id: media.gallery.default
targetEntityType: media
bundle: gallery
mode: default
content:
  field_credit:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 5
    region: content
  field_image:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 0
    region: content
  field_media_image:
    type: image
    label: visually_hidden
    settings:
      image_link: ''
      image_style: large
    third_party_settings: {  }
    weight: 0
    region: content
  field_media_slideshow:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_publish_date:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
    third_party_settings: {  }
    weight: 6
    region: content
  field_release_date:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
    third_party_settings: {  }
    weight: 11
    region: content
  field_tags:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 4
    region: content
  field_unpublish_date:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
    third_party_settings: {  }
    weight: 7
    region: content
  field_videos:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 8
    region: content
  flag_gallery_flag:
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: content
hidden:
  created: true
  langcode: true
  name: true
  search_api_excerpt: true
  thumbnail: true
  uid: true
