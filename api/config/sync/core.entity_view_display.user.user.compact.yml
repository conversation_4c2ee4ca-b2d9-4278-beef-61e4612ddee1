uuid: 57be15f9-5368-440a-b4f2-168ea7f92ced
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.user.compact
    - field.field.user.user.field_test
    - field.field.user.user.field_zoneinfo
    - field.field.user.user.user_picture
    - image.style.thumbnail
  module:
    - svg_image
    - user
_core:
  default_config_hash: '-cLsS3M3JycipXQt9rEb81_HxKneReoGuRh8ijcOPXs'
id: user.user.compact
targetEntityType: user
bundle: user
mode: compact
content:
  user_picture:
    type: image
    label: hidden
    settings:
      image_link: content
      image_style: thumbnail
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  field_test: true
  field_zoneinfo: true
  langcode: true
  member_for: true
  search_api_excerpt: true
