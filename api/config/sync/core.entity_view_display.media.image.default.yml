uuid: b46f80eb-7f4d-441b-b046-c32e8b0f6e2f
langcode: en
status: true
dependencies:
  config:
    - field.field.media.image.field_credit
    - field.field.media.image.field_description
    - field.field.media.image.field_focal_point_x
    - field.field.media.image.field_focal_point_y
    - field.field.media.image.field_fsk
    - field.field.media.image.field_media_image
    - image.style.large
    - media.type.image
  module:
    - svg_image
    - text
_core:
  default_config_hash: B1a2aBHmUulUZN6HrxITEH1tSResuVjXMBEv1lUBtOo
id: media.image.default
targetEntityType: media
bundle: image
mode: default
content:
  field_credit:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_description:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  field_focal_point_x:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 12
    region: content
  field_focal_point_y:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 13
    region: content
  field_fsk:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 11
    region: content
  field_media_image:
    type: image
    label: visually_hidden
    settings:
      image_link: ''
      image_style: large
      svg_attributes:
        width: null
        height: null
      svg_render_as_image: true
    third_party_settings: {  }
    weight: 1
    region: content
  flag_image_flag:
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: content
hidden:
  created: true
  langcode: true
  name: true
  search_api_excerpt: true
  thumbnail: true
  uid: true
