uuid: cab9c7f0-7cca-4f50-9e17-371e13bf54fd
langcode: en
status: true
dependencies: {  }
_core:
  default_config_hash: 6uqsWgLopdBAXIzy2SwBKR3p6a2GgNIsdteOAG9K-08
id: text_ngram_und_7_0_0
label: 'NGram Text Field'
minimum_solr_version: 7.0.0
custom_code: ngram
field_type_language_code: und
domains: {  }
field_type:
  name: text_ngram
  class: solr.TextField
  positionIncrementGap: 100
  analyzers:
    -
      type: index
      charFilters:
        -
          class: solr.MappingCharFilterFactory
          mapping: accents_und.txt
      tokenizer:
        class: solr.WhitespaceTokenizerFactory
      filters:
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_und.txt
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 1
          generateNumberParts: 1
          protected: protwords_und.txt
          splitOnCaseChange: 0
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 1
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
        -
          class: solr.NGramFilterFactory
          minGramSize: 2
          maxGramSize: 25
    -
      type: query
      charFilters:
        -
          class: solr.MappingCharFilterFactory
          mapping: accents_und.txt
      tokenizer:
        class: solr.WhitespaceTokenizerFactory
      filters:
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
unstemmed_field_type: null
spellcheck_field_type: null
collated_field_type: null
solr_configs: {  }
text_files: {  }
