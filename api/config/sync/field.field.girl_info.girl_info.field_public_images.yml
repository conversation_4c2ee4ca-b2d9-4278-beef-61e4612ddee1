uuid: e26b4b33-7435-4aae-987a-5a4a1759f9d4
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_public_images
  module:
    - image
    - pb_girl_info
id: girl_info.girl_info.field_public_images
field_name: field_public_images
entity_type: girl_info
bundle: girl_info
label: 'Public Images'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: 'public-images/[date:custom:Y]-[date:custom:m]/[random:number]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: false
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
