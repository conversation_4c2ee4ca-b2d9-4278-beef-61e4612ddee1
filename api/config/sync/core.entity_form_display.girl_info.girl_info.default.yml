uuid: 4968c04f-2181-45b4-9a69-8b85302a22f3
langcode: en
status: true
dependencies:
  config:
    - field.field.girl_info.girl_info.field_additional_girls
    - field.field.girl_info.girl_info.field_category
    - field.field.girl_info.girl_info.field_city
    - field.field.girl_info.girl_info.field_country
    - field.field.girl_info.girl_info.field_export_girl_des_tages
    - field.field.girl_info.girl_info.field_featured
    - field.field.girl_info.girl_info.field_from
    - field.field.girl_info.girl_info.field_image_count
    - field.field.girl_info.girl_info.field_main_focal_point_x
    - field.field.girl_info.girl_info.field_main_focal_point_y
    - field.field.girl_info.girl_info.field_notes
    - field.field.girl_info.girl_info.field_plus_access
    - field.field.girl_info.girl_info.field_province
    - field.field.girl_info.girl_info.field_public_images
    - field.field.girl_info.girl_info.field_second_focal_point_x
    - field.field.girl_info.girl_info.field_second_focal_point_y
    - field.field.girl_info.girl_info.field_tags
    - field.field.girl_info.girl_info.field_third_focal_point_x
    - field.field.girl_info.girl_info.field_third_focal_point_y
    - field.field.girl_info.girl_info.field_to
    - field.field.girl_info.girl_info.field_video_count
    - image.style.thumbnail
  module:
    - datetime
    - field_group
    - inline_entity_form
    - media_library
    - media_library_edit
    - pb_girl_info
    - svg_image
    - text
third_party_settings:
  field_group:
    group_tabs:
      children:
        - group_girls
        - group_basic
        - group_categorization
        - group_bilder
        - group_eigenschaften
        - group_gallerien
        - group_veroffentlichung
        - group_public_images
      label: Tabs
      region: content
      parent_name: ''
      weight: 6
      format_type: tabs
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        direction: vertical
        width_breakpoint: 640
    group_basic:
      children:
        - firstname
        - middlename
        - lastname
        - birthday
        - hometown
        - field_city
        - field_country
        - field_province
        - description
        - description_original
      label: General
      region: content
      parent_name: group_tabs
      weight: 5
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_eigenschaften:
      children:
        - height
        - weight
        - haircolor
        - eyecolor
        - cupsize
        - bustsize
        - hipsize
        - waistsize
      label: Properties
      region: content
      parent_name: group_tabs
      weight: 8
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_bilder:
      children:
        - main_images
        - non_nude_images
      label: Images
      region: content
      parent_name: group_tabs
      weight: 7
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_gallerien:
      children:
        - galleries
      label: Galleries
      region: content
      parent_name: group_tabs
      weight: 9
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_veroffentlichung:
      children:
        - release
        - field_from
        - field_to
      label: Publishing
      region: content
      parent_name: group_tabs
      weight: 10
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
        open: false
        weight: 0
    group_categorization:
      children:
        - field_tags
        - field_category
        - descriptor_country_ref
        - descriptor_year
        - descriptor_month
        - descriptor_week
      label: Categorization
      region: content
      parent_name: group_tabs
      weight: 6
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_girls:
      children:
        - girl
        - field_additional_girls
      label: Girls
      region: content
      parent_name: group_tabs
      weight: 4
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_public_images:
      children:
        - field_public_images
        - field_main_focal_point_x
        - field_main_focal_point_y
        - field_second_focal_point_x
        - field_second_focal_point_y
        - field_third_focal_point_x
        - field_third_focal_point_y
        - field_image_count
        - field_video_count
      label: 'Public Images and Information'
      region: content
      parent_name: group_tabs
      weight: 11
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
id: girl_info.girl_info.default
targetEntityType: girl_info
bundle: girl_info
mode: default
content:
  birthday:
    type: datetime_default
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  bustsize:
    type: number
    weight: 10
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  cupsize:
    type: options_select
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  description:
    type: string_textarea
    weight: 14
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  description_original:
    type: string_textarea
    weight: 15
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  descriptor_country:
    type: string_textfield
    weight: 14
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  descriptor_country_ref:
    type: entity_reference_autocomplete
    weight: 8
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  descriptor_month:
    type: number
    weight: 10
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  descriptor_title:
    type: string_textfield
    weight: 10
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  descriptor_week:
    type: number
    weight: 12
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  descriptor_year:
    type: number
    weight: 9
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  descriptors:
    type: string_textfield
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  eyecolor:
    type: options_select
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  field_additional_girls:
    type: entity_reference_autocomplete
    weight: 6
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_category:
    type: options_select
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  field_city:
    type: entity_reference_autocomplete_tags
    weight: 11
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_country:
    type: options_select
    weight: 12
    region: content
    settings: {  }
    third_party_settings: {  }
  field_export_girl_des_tages:
    type: boolean_checkbox
    weight: 2
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_featured:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_from:
    type: datetime_default
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  field_image_count:
    type: string_textfield
    weight: 13
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_main_focal_point_x:
    type: string_textfield
    weight: 7
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_main_focal_point_y:
    type: string_textfield
    weight: 8
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_notes:
    type: text_textarea
    weight: 23
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_plus_access:
    type: boolean_checkbox
    weight: 1
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_province:
    type: options_select
    weight: 13
    region: content
    settings: {  }
    third_party_settings: {  }
  field_public_images:
    type: image_image
    weight: 6
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_second_focal_point_x:
    type: string_textfield
    weight: 9
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_second_focal_point_y:
    type: string_textfield
    weight: 10
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_tags:
    type: entity_reference_autocomplete_tags
    weight: 6
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_third_focal_point_x:
    type: string_textfield
    weight: 11
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_third_focal_point_y:
    type: string_textfield
    weight: 12
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_to:
    type: datetime_default
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  field_video_count:
    type: string_textfield
    weight: 14
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  firstname:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  galleries:
    type: inline_entity_form_complex
    weight: 22
    region: content
    settings:
      form_mode: default
      override_labels: false
      label_singular: ''
      label_plural: ''
      allow_new: true
      allow_existing: true
      match_operator: CONTAINS
      allow_duplicate: false
      collapsible: false
      collapsed: false
      revision: false
    third_party_settings: {  }
  girl:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  haircolor:
    type: options_select
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  height:
    type: number
    weight: 4
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  hipsize:
    type: number
    weight: 11
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  hometown:
    type: entity_reference_autocomplete_tags
    weight: 10
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  images:
    type: entity_reference_autocomplete
    weight: 18
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 4
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  lastname:
    type: string_textfield
    weight: 7
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  main_images:
    type: media_library_widget
    weight: 8
    region: content
    settings:
      media_types: {  }
    third_party_settings:
      media_library_edit:
        show_edit: '1'
  middlename:
    type: string_textfield
    weight: 6
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  non_nude_images:
    type: media_library_widget
    weight: 10
    region: content
    settings:
      media_types: {  }
    third_party_settings:
      media_library_edit:
        show_edit: '1'
  release:
    type: datetime_timestamp
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  release_date:
    type: datetime_default
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 0
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  waistsize:
    type: number
    weight: 12
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  weight:
    type: number
    weight: 5
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
hidden:
  city: true
  country: true
  custom_access: true
  descriptor_category: true
  province: true
  publish_date: true
  tags: true
  unpublish_date: true
