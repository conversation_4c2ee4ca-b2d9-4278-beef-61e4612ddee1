uuid: 47524f41-e6ff-4a65-9ea2-ee649f136c21
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_media_reference
    - media.type.audio
    - media.type.document
    - media.type.e_paper
    - media.type.gallery
    - media.type.image
    - media.type.nexx_video
    - media.type.public_image
    - media.type.remote_video
    - media.type.video
    - node.type.test
id: node.test.field_media_reference
field_name: field_media_reference
entity_type: node
bundle: test
label: 'Media Reference'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      audio: audio
      document: document
      e_paper: e_paper
      gallery: gallery
      image: image
      nexx_video: nexx_video
      public_image: public_image
      remote_video: remote_video
      video: video
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: audio
field_type: entity_reference
