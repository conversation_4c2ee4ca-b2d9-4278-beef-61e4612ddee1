uuid: fcfb7f44-8e09-4158-b7f9-dc1edc75a331
langcode: en
status: true
dependencies:
  config:
    - field.field.node.coupon.field_ad_image
    - field.field.node.coupon.field_ad_subtitle
    - field.field.node.coupon.field_ad_title
    - field.field.node.coupon.field_body
    - field.field.node.coupon.field_button
    - field.field.node.coupon.field_columns
    - field.field.node.coupon.field_coupon_cards
    - field.field.node.coupon.field_hero_
    - field.field.node.coupon.field_logo
    - field.field.node.coupon.field_mobile_hero_image
    - field.field.node.coupon.field_notice
    - field.field.node.coupon.field_slug
    - field.field.node.coupon.field_title
    - image.style.thumbnail
    - node.type.coupon
  module:
    - content_moderation
    - field_group
    - link
    - paragraphs
    - scheduler
    - scheduler_content_moderation_integration
    - svg_image
    - text
third_party_settings:
  field_group:
    group_hero:
      children:
        - field_hero_
        - field_mobile_hero_image
        - field_logo
        - field_title
        - field_body
        - field_button
      label: Hero
      region: content
      parent_name: ''
      weight: 2
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        description: ''
        required_fields: true
    group_card_grif:
      children:
        - field_columns
        - field_coupon_cards
      label: 'Card Grid'
      region: content
      parent_name: ''
      weight: 3
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        description: ''
        required_fields: true
    group_ad:
      children:
        - field_ad_title
        - field_ad_subtitle
        - field_ad_image
      label: E-Paper
      region: content
      parent_name: ''
      weight: 5
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        description: ''
        required_fields: true
id: node.coupon.default
targetEntityType: node
bundle: coupon
mode: default
content:
  field_ad_image:
    type: image_image
    weight: 26
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_ad_subtitle:
    type: string_textfield
    weight: 8
    region: content
    settings:
      size: 60
      placeholder: 'Das Playboy E-Paper im Wert von 8,49 Euro gratis!'
    third_party_settings: {  }
  field_ad_title:
    type: string_textfield
    weight: 7
    region: content
    settings:
      size: 60
      placeholder: 'Jeden Monat inklusive'
    third_party_settings: {  }
  field_body:
    type: text_textarea
    weight: 15
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_button:
    type: link_default
    weight: 16
    region: content
    settings:
      placeholder_url: 'https://api.premium.playboy.de/user/login'
      placeholder_title: 'Jetzt freischalten'
    third_party_settings: {  }
  field_columns:
    type: options_select
    weight: 28
    region: content
    settings: {  }
    third_party_settings: {  }
  field_coupon_cards:
    type: entity_reference_paragraphs
    weight: 29
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: coupon_card
    third_party_settings: {  }
  field_hero_:
    type: image_image
    weight: 11
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_logo:
    type: image_image
    weight: 13
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_mobile_hero_image:
    type: image_image
    weight: 12
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_notice:
    type: text_textarea
    weight: 4
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_slug:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_title:
    type: string_textfield
    weight: 14
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 12
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_on:
    type: datetime_timestamp_no_default
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_state:
    type: scheduler_moderation
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  scheduler_settings:
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  unpublish_state:
    type: scheduler_moderation
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  field_new_price_card_2: true
  field_new_price_card_4: true
  langcode: true
  path: true
  promote: true
  sticky: true
  uid: true
