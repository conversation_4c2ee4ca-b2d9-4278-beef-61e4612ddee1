uuid: 0e93caed-02ed-4782-b8ca-60abfc4b5538
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.feature_module.field_button
    - field.field.paragraph.feature_module.field_button_bool
    - field.field.paragraph.feature_module.field_category_bool
    - field.field.paragraph.feature_module.field_category_reference
    - field.field.paragraph.feature_module.field_descriptor_country
    - field.field.paragraph.feature_module.field_girl_info
    - field.field.paragraph.feature_module.field_grid_layout
    - field.field.paragraph.feature_module.field_sorting
    - field.field.paragraph.feature_module.field_title
    - paragraphs.paragraphs_type.feature_module
  module:
    - field_group
    - link
third_party_settings:
  field_group:
    group_category:
      children:
        - field_category_bool
        - field_category_reference
        - field_descriptor_country
        - field_sorting
      label: Category
      region: content
      parent_name: ''
      weight: 1
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: true
        description: ''
        required_fields: true
    group_grid:
      children:
        - field_grid_layout
      label: Grid
      region: content
      parent_name: ''
      weight: 2
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: true
        description: ''
        required_fields: true
    group_girl_info:
      children:
        - field_girl_info
      label: Girl-Info
      region: content
      parent_name: ''
      weight: 3
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: true
        description: ''
        required_fields: true
    group_button:
      children:
        - field_button_bool
        - field_button
      label: Button
      region: content
      parent_name: ''
      weight: 4
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: true
        description: ''
        required_fields: true
id: paragraph.feature_module.default
targetEntityType: paragraph
bundle: feature_module
mode: default
content:
  field_button:
    type: link_default
    weight: 6
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_button_bool:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_category_bool:
    type: boolean_checkbox
    weight: 2
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_category_reference:
    type: entity_reference_autocomplete
    weight: 3
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_descriptor_country:
    type: entity_reference_autocomplete
    weight: 4
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_girl_info:
    type: entity_reference_autocomplete
    weight: 4
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_grid_layout:
    type: options_select
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_sorting:
    type: options_select
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  status: true
