uuid: bc0ddace-2cb8-4488-8a6a-8c6416e58255
langcode: en
status: true
dependencies:
  config:
    - field.field.media.e_paper.field_media_file
    - media.type.e_paper
  module:
    - file
    - path
id: media.e_paper.default
targetEntityType: media
bundle: e_paper
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_media_file:
    type: file_generic
    weight: 0
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 100
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
