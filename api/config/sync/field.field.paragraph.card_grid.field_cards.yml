uuid: b67d297e-4db1-4172-b1d0-39dc32f84351
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cards
    - paragraphs.paragraphs_type.card_grid
  module:
    - entity_reference_revisions
id: paragraph.card_grid.field_cards
field_name: field_cards
entity_type: paragraph
bundle: card_grid
label: Cards
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles: {  }
    negate: 0
    target_bundles_drag_drop:
      card_grid:
        weight: 5
        enabled: false
      homepage_features:
        weight: 6
        enabled: false
      nexx_video:
        weight: 7
        enabled: false
      reusable_element_card:
        weight: 8
        enabled: true
field_type: entity_reference_revisions
