uuid: 92f44214-8f2b-419f-9a30-fc769b80e4c9
langcode: en
status: true
dependencies: {  }
_core:
  default_config_hash: ESG2x3MSLc7DomtiTVDA4Ye7Bsz0Og_LE1C1h6RQwx0
id: pb_girl_info
class: null
field_plugin_method: null
cck_plugin_method: null
migration_tags: null
migration_group: pb_girl_info
label: 'Import girl info entities'
source:
  plugin: url
  glob: true
  urls:
    - 'public://migration/girl_info/data/infos/*.json'
  data_fetcher_plugin: file
  data_parser_plugin: json
  item_selector: girlInfos
  fields:
    -
      name: girl_info_id
      label: 'Primary girl info id'
      selector: id
    -
      name: uuid
      label: Uuid
      selector: uuid
    -
      name: girl_id
      label: 'Girl id'
      selector: girl
    -
      name: state
      label: State
      selector: state
    -
      name: publish_date
      label: 'Publish date'
      selector: publishedFrom
    -
      name: unpublish_date
      label: 'Unpublish date'
      selector: publishedThru
    -
      name: title
      label: Title
      selector: title
    -
      name: firstname
      label: firstname
      selector: firstname
    -
      name: middlename
      label: middlename
      selector: middlename
    -
      name: lastname
      label: lastname
      selector: lastname
    -
      name: bustsize
      label: bustsize
      selector: bustsize
    -
      name: waistsize
      label: waistsize
      selector: waistsize
    -
      name: hipsize
      label: hipsize
      selector: hipsize
    -
      name: cupsize
      label: cupsize
      selector: cupsize
    -
      name: height
      label: height
      selector: height
    -
      name: weight
      label: weight
      selector: weight
    -
      name: haircolor
      label: haircolor
      selector: haircolor
    -
      name: eyecolor
      label: eyecolor
      selector: eyecolor
    -
      name: birthday
      label: birthday
      selector: birthday
    -
      name: hometown
      label: hometown
      selector: hometown
    -
      name: description
      label: description
      selector: description
    -
      name: descriptor_category
      label: 'Descriptor Category/Title'
      selector: descriptors/0/title
    -
      name: descriptor_year
      label: 'Descriptor year'
      selector: descriptors/0/year
    -
      name: descriptor_month
      label: 'Descriptor month'
      selector: descriptors/0/month
    -
      name: descriptor_week
      label: 'Descriptor week'
      selector: descriptors/0/week
    -
      name: descriptor_country_ref
      label: 'Descriptor country code'
      selector: descriptors/0/country
    -
      name: city
      label: City
      selector: city
    -
      name: province
      label: Province
      selector: province
    -
      name: country
      label: Country
      selector: country
    -
      name: tags
      label: Tags
      selector: tags
    -
      name: containers
      label: Containers
      selector: containers
    -
      name: releasedate
      label: 'Release date'
      selector: released
    -
      name: main_images
      label: 'Main Image'
      selector: mainImage/normal/id
    -
      name: nn_images
      label: 'Non-nude Images'
      selector: mainImage/nonNude/id
  ids:
    uuid:
      type: string
process:
  id: girl_info_id
  field_from:
    plugin: callback
    callable: pb_girl_info_date_field_migrate_callback
    source: publish_date
  field_to:
    plugin: callback
    callable: pb_girl_info_date_field_migrate_callback
    source: unpublish_date
destination:
  plugin: 'entity:girl_info'
migration_dependencies:
  required:
    - pb_girl_gallery
  optional: {  }
