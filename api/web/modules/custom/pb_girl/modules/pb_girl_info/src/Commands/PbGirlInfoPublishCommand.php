<?php

namespace Drupal\pb_girl_info\Commands;

use Drupal\Core\File\FileSystem;
use Drush\Commands\DrushCommands;
use Drupal\Core\Datetime\DrupalDateTime;
use Drupal\datetime\Plugin\Field\FieldType\DateTimeItemInterface;
use Drupal\media\Entity\Media;
use Drupal\pb_girl\Entity\Girl;
use Drupal\pb_girl_info\Entity\GirlInfo;

/**
 * Drush commands for pb girl info migration tasks.
 *
 * @package Drupal\pb_girl_info\Commands
 */
class PbGirlInfoPublishCommand extends DrushCommands
{

  /**
   * Constructs a new PbGirlInfoMigrationCommand object.
   */
  public function __construct()
  {
    parent::__construct();
  }


  /**
   * Publishes entities which are due.
   *
   * @command pb:publish
   * @aliases pb:p
   */
  public function publish()
  {
    // get now date in the right format
    $date = new DrupalDateTime('now');
    $date->setTimezone(new \DateTimezone(DateTimeItemInterface::STORAGE_TIMEZONE));
    $formatted = $date->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);

    $log = [
      'girls' => [
        'published' => [],
        'unpublished' => []
      ],
      'infos' => [
        'published' => [],
        'unpublished' => []
      ],
      'galleries' => [
        'published' => [],
        'unpublished' => []
      ],
    ];

    /* ##########
    GIRLS
    ############# */

    // check what entities to publish
    $girl_group = \Drupal::entityQuery('girl')
      ->orConditionGroup()
      ->condition('field_to', $formatted, '>')
      ->condition('field_to', null, 'IS');

    $publish_query = \Drupal::entityQuery('girl')
      ->condition('status', 0)
      ->condition('field_from', $formatted, '<=')
      ->condition($girl_group)
      ->execute();

    $publish_girls =  Girl::loadMultiple($publish_query);

    if (count($publish_girls) > 0) {
      foreach ($publish_girls as $girl) {
        array_push($log['girls']['published'], ['name' => $girl->getName(), 'id' => $girl->id()]);
        $girl->setPublished();
        $girl->save();
      }
    }

    // check what entities to publish
    $unpublish_query = \Drupal::entityQuery('girl')
      ->condition('status', 1)
      ->condition('field_to', $formatted, '<=')
      ->execute();

    $unpublish_girls =  Girl::loadMultiple($unpublish_query);

    if (count($unpublish_girls) > 0) {
      foreach ($unpublish_girls as $girl) {
        array_push($log['girls']['unpublished'], ['name' => $girl->getName(), 'id' => $girl->id()]);
        $girl->setUnpublished();
        $girl->save();
      }
    }





    /* ##########
    INFOS
    ############# */

    // check what entities to publish
    $infos_group = \Drupal::entityQuery('girl_info')
      ->orConditionGroup()
      ->condition('field_to', $formatted, '>')
      ->condition('field_to', null, 'IS');

    $publish_query = \Drupal::entityQuery('girl_info')
      ->condition('status', 0)
      ->condition('field_from', $formatted, '<=')
      ->condition($infos_group)
      ->execute();

    $publish_infos =  GirlInfo::loadMultiple($publish_query);

    if (count($publish_infos) > 0) {
      foreach ($publish_infos as $info) {
        array_push($log['infos']['published'], ['name' => $info->getName(), 'id' => $info->id()]);
        $info->setPublished();
        $info->save();
      }
    }

    // check what entities to publish
    $unpublish_query = \Drupal::entityQuery('girl_info')
      ->condition('status', 1)
      ->condition('field_to', $formatted, '<=')
      ->execute();

    $unpublish_infos =  GirlInfo::loadMultiple($unpublish_query);

    if (count($unpublish_infos) > 0) {
      foreach ($unpublish_infos as $info) {
        array_push($log['infos']['unpublished'], ['name' => $info->getName(), 'id' => $info->id()]);
        $info->setUnpublished();
        $info->save();
      }
    }




    /* ##########
    GALLERIES
    ############# */

    // check what entities to publish
    $gallery_group = \Drupal::entityQuery('media')
      ->orConditionGroup()
      ->condition('field_unpublish_date', $formatted, '>')
      ->condition('field_unpublish_date', null, 'IS');

    $publish_query = \Drupal::entityQuery('media')
      ->condition('status', 0)
      ->condition('bundle', 'gallery')
      ->condition('field_publish_date', $formatted, '<=')
      ->condition($gallery_group)
      ->execute();

    $publish_galleries =  Media::loadMultiple($publish_query);

    if (count($publish_galleries) > 0) {
      foreach ($publish_galleries as $gallery) {
        array_push($log['galleries']['published'], ['name' => $gallery->getName(), 'id' => $gallery->id()]);
        $gallery->setPublished();
        $gallery->save();
      }
    }

    // check what entities to publish
    $unpublish_query = \Drupal::entityQuery('media')
      ->condition('status', 1)
      ->condition('bundle', 'gallery')
      ->condition('field_unpublish_date', $formatted, '<=')
      ->execute();

    $unpublish_galleries =  Media::loadMultiple($unpublish_query);

    if (count($unpublish_galleries) > 0) {
      foreach ($unpublish_galleries as $gallery) {
        array_push($log['galleries']['unpublished'], ['name' => $gallery->getName(), 'id' => $gallery->id()]);
        $gallery->setUnpublished();
        $gallery->save();
      }
    }

    \Drupal::logger('pb_girl_info')->notice(json_encode($log));
  }
}