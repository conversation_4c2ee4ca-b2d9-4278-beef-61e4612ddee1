smal_auth.authenticate:
  path: '/sso/authenticate'
  defaults:
    _title: 'Playboy SSO authentication'
    _controller: '\Drupal\smal_auth\Controller\SmalAuthController::authenticate'
  requirements:
    _permission: 'access content'
  options:
    no_cache: true
smal_auth.check_session:
  path: '/sso/check-session'
  defaults:
    _title: 'Playboy SSO session check'
    _controller: '\Drupal\smal_auth\Controller\SmalAuthController::checkSession'
  options:
    no_cache: 'TRUE'
  requirements:
    _permission: 'access content'
smal_auth.external_logout:
  path: '/sso/logout'
  defaults:
    _controller: '\Drupal\smal_auth\Controller\SmalAuthController::logout'
  methods: [POST]
  requirements:
    _access: 'TRUE'
  options:
    no_cache: 'TRUE'
smal_auth.girlinfo_to_girl:
  path: '/redirect/girlinfo/{id}'
  defaults:
    _title: 'Playboy girl info redirect'
    _controller: '\Drupal\smal_auth\Controller\SmalAuthController::girlinfo_to_girl'
  requirements:
    _permission: 'access content'
smal_auth.girlinfo_to_girl_gallery:
  path: '/redirect/girlinfo/{id}/gallery'
  defaults:
    _title: 'Playboy girl gallery redirect'
    _controller: '\Drupal\smal_auth\Controller\SmalAuthController::girlinfo_to_girl_gallery'
  requirements:
    _permission: 'access content'
