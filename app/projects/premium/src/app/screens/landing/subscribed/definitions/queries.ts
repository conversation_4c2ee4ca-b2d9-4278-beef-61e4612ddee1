import gql from 'graphql-tag';
export const NEW_PHOTOS_COUNT = gql`
  query NewContentCount($timestamp: String!) {
    pbInfoWithLastGalleryImageCount(
      filter: { field_publish_date_value_2: { value: $timestamp } }
    ) {
      count
    }
  }
`;
export const NEW_VIDEOS_COUNT = gql`
  query NewContentCount($timestamp: String!) {
    pbInfoWithLastGalleryVideoCount(
      filter: { field_publish_date_value_2: { value: $timestamp } }
    ) {
      count
    }
  }
`;
export const ALL_PHOTOS_COUNT = gql`
  {
    pbInfoWithLastGalleryImageCount {
      count
    }
  }
`;
export const ALL_VIDEOS_COUNT = gql`
  {
    pbInfoWithLastGalleryVideoCount {
      count
    }
  }
`;
export const LANDING_HEADER_QUERY = gql`
  {
    pbInfoWithLastGallery(pageSize: 4, sortBy: FIELD_PUBLISH_DATE_VALUE) {
      results {
        entityId
        reverseGalleriesGirlInfo {
          entities {
            entityLabel
            entityId
            ... on GirlInfo {
              id
              entityId
              fieldPublicImages {
                url
              }
              fieldPlusAccess
              descriptorYear
              descriptorMonth
              fieldMainFocalPointX
              fieldMainFocalPointY
              queryGirl {
                entities {
                  entityId
                  ... on Girl {
                    id
                    name
                  }
                }
              }
              fieldCategory {
                entity {
                  entityLabel
                  name
                }
              }
            }
          }
        }
        entityLabel
        fieldImage {
          entity {
            ... on MediaImage {
              fieldMediaImage {
                url
              }
              fieldFocalPointX
              fieldFocalPointY
            }
          }
        }
      }
    }
  }
`;
export const PERSONAL_CONTENT_QUERY = gql`
  query NewContentCount($timestamp: PublishDateMultiViewFilterInput!) {
    pbInfoWithLastGallery(
      pageSize: 4
      sortBy: FIELD_PUBLISH_DATE_VALUE
      filter: { publish_date: $timestamp }
    ) {
      entities {
        ... on MediaGallery {
          mid
          uuid
          name
          reverseGalleriesGirlInfo {
            entities {
              ... on GirlInfo {
                id
                name
                fieldCategory {
                  entity {
                    name
                  }
                }
              }
            }
          }
          fieldMediaImage {
            url
          }
          __typename
        }
        ... on MediaImage {
          mid
          entityLabel
          fieldMediaImage {
            url
            derivative(style: LARGE) {
              url
            }
          }
          reverseMainImagesGirl {
            entities {
              entityLabel
              entityId
            }
          }
          __typename
        }
        ... on MediaNexxVideo {
          mid
          fieldNexxId
          __typename
          fieldPreviewImage {
            entity {
              entityLabel
              ... on MediaImage {
                fieldMediaImage {
                  url
                  derivative(style: LARGE) {
                    url
                  }
                }
              }
            }
          }
          entityLabel
        }
      }
    }
  }
`;
export const GIRLS_OF_THE_DAY_QUERY = gql`
  {
    pbGalerieDesTages {
      results {
        entityLabel
        ... on NodeGirlDesTagesGalleries {
          entityLabel
          fieldGirlInfos {
            entity {
              ... on GirlInfo {
                id
                name
                fieldPublicImages {
                  url
                }
                fieldPlusAccess
                fieldFeatured
                girl {
                  targetId
                }
                fieldCategory {
                  entity {
                    name
                  }
                }
                descriptorYear
                descriptorMonth
                fieldPublicImagesLow: fieldPublicImages {
                  derivative(style: XLARGE) {
                    url
                  }
                }
                fieldMainFocalPointX
                fieldMainFocalPointY
              }
            }
          }
        }
      }
    }
  }
`;
export const SPECIAL_CONTENT_QUERY = gql`
  {
    pbFeatured {
      results {
        ... on MediaGallery {
          fieldImage {
            entity {
              ... on MediaImage {
                fieldMediaImage {
                  url
                }
                fieldFocalPointX
                fieldFocalPointY
              }
            }
          }
        }
        reverseGalleriesGirlInfo(sort: { field: "release", direction: DESC }) {
          entities {
            ... on GirlInfo {
              id
              name
              fieldPlusAccess
              fieldFeatured
              girl {
                targetId
              }
              fieldCategory {
                entity {
                  name
                }
              }
              descriptorYear
              descriptorMonth
              fieldPublicImages {
                url
              }
              fieldMainFocalPointX
              fieldMainFocalPointY
            }
          }
        }
      }
    }
  }
`;
export const POPULAR_CONTENT_QUERY = gql`
  {
    pbInfoWithLastGallery(
      sortBy: DAYCOUNT
      pageSize: 3
      sortDirection: DESC
      page: 1
    ) {
      results {
        ... on MediaGallery {
          fieldImage {
            entity {
              ... on MediaImage {
                fieldMediaImage {
                  url
                }
                fieldFocalPointX
                fieldFocalPointY
              }
            }
          }
        }
        reverseGalleriesGirlInfo {
          entities {
            ... on GirlInfo {
              id
              name
              fieldFeatured
              girl {
                targetId
              }
              fieldCategory {
                entity {
                  name
                }
              }
              fieldPublicImages {
                url
              }
              fieldMainFocalPointX
              fieldMainFocalPointY
              descriptorYear
              descriptorMonth
            }
          }
        }
      }
    }
  }
`;

/*

field_category
:
["2245"]
0
:
"2245"
page
:
0
pageSize
:
24
sortDirection
:
"DESC"
sortField
:
"FIELD_PUBLISH_DATE_VALUE"
 */
export const COVERSTAR_CONTENT_QUERY = gql`
  {
    pbInfoWithLastGallery(
      sortBy: FIELD_PUBLISH_DATE_VALUE
      pageSize: 4
      sortDirection: DESC
      page: 0
    ) {
      results {
        ... on MediaGallery {
          fieldImage {
            entity {
              ... on MediaImage {
                fieldMediaImage {
                  url
                }
                fieldFocalPointX
                fieldFocalPointY
              }
            }
          }
        }
        reverseGalleriesGirlInfo {
          entities {
            ... on GirlInfo {
              id
              name
              fieldFeatured
              girl {
                targetId
              }
              fieldCategory {
                entity {
                  name
                }
              }
              fieldPublicImages {
                url
              }
              fieldMainFocalPointX
              fieldMainFocalPointY
              descriptorYear
              descriptorMonth
            }
          }
        }
      }
    }
  }
`;
