<!--BREADCRUMBS-->
@if (metaInformation()) {
  @if (girl.value() && metaInformation()?.category) {
    <lib-breadcrumbs
      class="mt-8 ml-5 md:ml-32 mr-5 md:mr-32 md:mt-0"
      [breadcrumbs]="[
        {
          link: '/categories/' + (metaInformation().category | categorySlug),
          label: metaInformation().category,
        },
        { link: '/girl/' + girl_id(), label: girl.value()?.name },
      ]"
    >
    </lib-breadcrumbs>
  }
}

<!--HERO IMAGE-->
<div class="gallery-hero">
  <div class="sm:h-auto h-screen-3/4 flex flex-col aspect-auto sm:aspect-2/1">
    @if (girl.value()) {
      @let gallerySource =
        isShootingGallery() && currentGirlGallery()
          ? currentGirlGallery()
          : girl.value().reverseGirlGirlInfo?.entities?.at(0);
      <app-gallery-card
        class="w-full h-full max-h-full"
        [image]="gallerySource.fieldPublicImages?.at(0)?.url || ''"
        [focalPoint]="{
          x: gallerySource.fieldMainFocalPointX,
          y: gallerySource.fieldMainFocalPointY,
        }"
        [withoutCard]="true"
      ></app-gallery-card>
    }
  </div>
</div>

<!--INFO BLOCK: Girl/Shooting name, Meta information-->
@if (girl.value()) {
  <div class="md:mx-31 flex flex-col mx-auto mt-10 md:mt-20">
    <div class="flex items-center justify-start mb-4 md:mb-6">
      <h2 class="inline-flex">
        {{
          isShootingGallery() ? currentGirlGallery().name : girl.value().name
        }}
      </h2>
      @if (
        isShootingGallery() &&
        subscriptionType() !== "all-access" &&
        currentGirlGallery().fieldPlusAccess !== true
      ) {
        <app-lock-badge
          class="mx-6 cursor-pointer"
          [paywallImages]="girlInfoGalleries()?.at(0)?.fieldPublicImages"
        ></app-lock-badge>
      } @else {
        <app-favorite-star
          [paywallImages]="
            isShootingGallery()
              ? currentGirlGallery().fieldPublicImages
              : girlInfoGalleries()?.at(0)?.fieldPublicImages
          "
          class="inline-flex justify-center items-center rounded-full bg-black bg-opacity-75 h-12 w-12 p-0 mx-6"
          [type]="isShootingGallery() ? 'girl-infos' : 'girl'"
          [id]="
            isShootingGallery()
              ? gallery_id()?.toString()
              : girl_id()?.toString()
          "
          #favStar
          [title]="
            favStar.isFav
              ? 'Von Favoriten entfernen'
              : 'Zu Favoriten hinzufügen'
          "
        ></app-favorite-star>
      }
    </div>
    @if (isShootingGallery() && metaInformation().category) {
      <h4 class="sm:mb-3">
        {{ metaInformation().category }}
        @if (currentGirlGallery().descriptorMonth) {
          · {{ currentGirlGallery().descriptorMonth | month }}
        }
        @if (currentGirlGallery().descriptorYear) {
          {{ currentGirlGallery().descriptorYear }}
        }
      </h4>
    }
    @if (metaInformation()) {
      <h4 class="text-golden sm:mb-3">
        @if (metaInformation().galleries) {
          {{ metaInformation().galleries }} Galerie{{
            metaInformation().galleries === 1 ? "" : "n"
          }}
        }
        @if (
          metaInformation().galleries &&
          (metaInformation().images || metaInformation().videos)
        ) {
          ·
        }
        @if (metaInformation().images) {
          {{ metaInformation().images }} Bild{{
            metaInformation().images === 1 ? "" : "er"
          }}
        }
        @if (metaInformation().images && metaInformation().videos) {
          ·
        }
        @if (metaInformation().videos) {
          {{ metaInformation().videos }} Video{{
            metaInformation().videos === 1 ? "" : "s"
          }}
        }
        @if (getCredit(currentGirlGallery())) {
          {{
            metaInformation().images || metaInformation().videos ? " · " : ""
          }}
          {{
            getCredit(currentGirlGallery())
              ? "Credit: " + getCredit(currentGirlGallery())
              : ""
          }}
        }
      </h4>
    }
    <!--    GIRL ATTRIBUTES & GIRL DESCRIPTION TEXT-->
    @if (!isShootingGallery()) {
      @if (metaInformation() && metaInformation().attributes.length) {
        <div class="mt-8 text-lg">
          <div
            class="font-inter justify-center md:my-2 py-6 inline-grid grid-cols-2 auto-rows-auto gap-x-6"
          >
            @for (attribute of metaInformation().attributes; track attribute) {
              <p class="w-32">{{ attribute[0] }}:</p>
              <p class="flex flex-1">
                {{ attribute[1] }}
              </p>
            }
          </div>
        </div>
      }

      <div class="mt-8 font-georgia text-lg">
        @if (metaInformation().description) {
          <div [innerHTML]="metaInformation().description"></div>
        }
      </div>
    }
  </div>
}

<!--GALLERY SECTION ON GIRL PAGE-->
@if (!isShootingGallery()) {
  <div class="mx-auto md:mx-31">
    <hr class="w-full my-15 border-golden" />
    <p class="text-4xl font-bauer mb-10 md:mb-20">Galerien</p>
  </div>
  <div
    class="gallery-list mt-6 grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-6 mb-52"
    [class.single]="girlInfoGalleries().length === 1"
    [class.triple]="girlInfoGalleries().length >= 3"
  >
    @for (girlInfo of girlInfoGalleries(); track girlInfo) {
      <div class="w-full overflow-hidden flex flex-col">
        @if (!isShootingGallery()) {
          <a [routerLink]="['/girl', girl_id(), girlInfo.id]">
            <app-gallery-card
              class="w-full shooting-preview rounded-lg overflow-hidden"
              [name]="girlInfo.fieldCategory?.entity?.name"
              [meta]="{
                images: girlInfo?.fieldImageCount,
                videos: girlInfo?.fieldVideoCount,
                girlInfos: 0,
              }"
              [title]="
                (girlInfo.descriptorMonth | month: 'short') +
                ' ' +
                girlInfo.descriptorYear
              "
              [credit]="getCredit(girlInfo) ? getCredit(girlInfo) : ''"
              [imageRatio]="16 / 9"
              [focalPoint]="{
                x: girlInfo.fieldMainFocalPointX,
                y: girlInfo.fieldMainFocalPointY,
              }"
              [image]="girlInfo.fieldPublicImages[0].url"
            >
            </app-gallery-card>
          </a>
        }
      </div>
    }
  </div>
}

<!--GALLERY OF SHOOTING-->
@if (isShootingGallery()) {
  @for (girlInfo of girlInfoGalleries(); track girlInfo) {
    @if (girlInfo.id == gallery_id()) {
      <div class="w-full overflow-hidden flex flex-col">
        @if (girlInfo.displayItems; as articles) {
          @if (
            articles.length > 0 &&
            (subscriptionType() === "all-access" ||
              currentGirlGallery().fieldPlusAccess === true)
          ) {
            <div
              class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 mt-10 sm:mt-20 gallery-list"
            >
              <!-- Images -->
              @for (
                article of articles
                  | slice: 0 : (showAllGalleryImages ? undefined : 8);
                track article
              ) {
                <a
                  class="flex group"
                  [routerLink]="[
                    '/girl-info',
                    girlInfo.id,
                    article.entity?.mid,
                  ]"
                  [queryParams]="{ filter: '' }"
                >
                  <app-article-preview
                    [previewData]="article.entity"
                    [groupHoverEffect]="true"
                    class="flex w-full rounded-lg overflow-hidden"
                    [focalPoint]="article.entity | focalPoint"
                    [image]="
                      article.entity?.fieldMediaImage?.url ||
                      article.entity?.fieldPreviewImage?.entity?.fieldMediaImage
                        ?.url
                    "
                    tag="Top 3"
                    [autoSize]="false"
                    [nexxID]="article.entity?.fieldNexxId"
                  >
                  </app-article-preview>
                </a>
              }
            </div>
            <!-- Button to toggle full/less view if user can see all photos -->
            @if (girlInfo.displayItems.length > 8) {
              <div
                class="flex my-6 md:my-8 lg:my-12 px-3 md:px-0 mx-6 justify-center md:justify-end"
              >
                <a
                  class="uppercase font-inter text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
                  (click)="showAllGalleryImages = !showAllGalleryImages"
                >
                  @if (showAllGalleryImages) {
                    Weniger anzeigen
                  } @else {
                    Alle anzeigen
                  }
                </a>
              </div>
            }
          } @else {
            <!--            User can only see public images -->
            <div
              class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 md:gap-6 mt-10 sm:mt-20 gallery-list"
            >
              @for (
                image of girlInfo.fieldPublicImages;
                track image;
                let index = $index
              ) {
                <div
                  class="inline-flex group w-full rounded-lg overflow-hidden"
                  [class.cursor-pointer]="
                    currentGirlGallery().fieldPlusAccess !== true
                  "
                  (click)="
                    currentGirlGallery().fieldPlusAccess !== true
                      ? showPaywall(
                          currentGirlGallery().fieldPlusAccess,
                          girlInfo.fieldPublicImages
                        )
                      : null
                  "
                >
                  <app-article-preview
                    [groupHoverEffect]="true"
                    [imageRatio]="1"
                    class="flex w-full rounded-lg overflow-hidden"
                    [focalPoint]="
                      index === 0
                        ? {
                            x: girlInfo.fieldMainFocalPointX,
                            y: girlInfo.fieldMainFocalPointY,
                          }
                        : index === 1
                          ? {
                              x: girlInfo.fieldMainFocalPointX,
                              y: girlInfo.fieldMainFocalPointY,
                            }
                          : index === 2
                            ? {
                                x: girlInfo.fieldMainFocalPointX,
                                y: girlInfo.fieldMainFocalPointY,
                              }
                            : null
                    "
                    [image]="image.url"
                    [autoSize]="false"
                  >
                  </app-article-preview>
                </div>
              }
            </div>

            <!--  User can only see public images: show button for paywall trigger -->
            @if (currentGirlGallery()?.fieldPlusAccess !== true) {
              <div
                class="flex my-6 md:my-8 lg:my-12 px-3 md:px-0 mx-6 justify-end"
              >
                <a
                  class="uppercase font-inter text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
                  (click)="
                    showPaywall(
                      currentGirlGallery()?.fieldPlusAccess,
                      girlInfo.fieldPublicImages
                    )
                  "
                >
                  Alle anzeigen
                </a>
              </div>
            }
          }
        }
      </div>
    }
  }

  <!--  Shooting description-->
  <div class="md:mx-31 mx-auto">
    @if (girl.value()) {
      <div class="mt-3 sm:mt-8 font-georgia text-lg">
        @if (currentGirlGallery().description) {
          <div [innerHTML]="currentGirlGallery().description"></div>
        }
      </div>
    }

    <!--    Models in this gallery, main entity & fieldAdditionalGirls-->
    @if (headerGallery().length > 0 && girl.value()) {
      <h3 id="title-proposal" class="my-10 md:mt-20 w-full text-left">
        In dieser Galerie
      </h3>

      <div class="my-10 md:my-15 flex flex-wrap gap-8">
        <div class="model-preview">
          <a class="inline-block" [routerLink]="['/girl', girl_id()]">
            @let firstImage = headerGallery().at(0);
            @if (firstImage) {
              <lib-model-card
                [image]="firstImage.image"
                [name]="girl.value().name"
              ></lib-model-card>
            }
          </a>
        </div>

        @for (
          additionalGirl of currentGirlGallery()?.fieldAdditionalGirls;
          track additionalGirl.targetId
        ) {
          <div class="model-preview">
            <a
              class="inline-block"
              [routerLink]="['/girl', additionalGirl.targetId]"
            >
              @let additionalGirlImg =
                additionalGirl?.entity?.reverseGirlGirlInfo?.entities[0]?.fieldPublicImages?.at(
                  0
                )?.url ||
                additionalGirl.entity.mainImages?.at(0)?.entity?.fieldMediaImage
                  ?.url;
              <lib-model-card
                [image]="
                  additionalGirlImg
                    ? additionalGirlImg
                    : currentGirlGallery().fieldPublicImages?.at(0)?.url
                "
                [name]="additionalGirl.entity?.name"
              ></lib-model-card>
            </a>
          </div>
        }
      </div>
    }
  </div>
}
