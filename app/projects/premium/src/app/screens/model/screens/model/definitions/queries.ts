import gql from 'graphql-tag';

export const GIRL_DETAIL_DATA_QUERY = gql`
  query GetGirlDetailData($girlId: String!) {
    girlById(id: $girlId) {
      name
      mainImages {
        entity {
          ... on MediaImage {
            fieldMediaImage {
              url
            }
            fieldFocalPointX
            fieldFocalPointY
          }
        }
      }
      reverseGirlGirlInfo(
        sort: { field: "release", direction: DESC }
        filter: { conditions: { field: "status", value: "1" } }
      ) {
        entities {
          ... on GirlInfo {
            changed
            id
            fieldPublicImages {
              url
            }
            reverseFieldGirlInfosNode {
              entities {
                entityId
              }
            }
            fieldPlusAccess
            fieldMainFocalPointX
            fieldMainFocalPointY
            fieldImageCount
            fieldVideoCount
            fieldAdditionalGirls {
              targetId
              entity {
                id
                name
                mainImages {
                  entity {
                    ... on MediaImage {
                      fieldMediaImage {
                        url
                      }
                      fieldFocalPointX
                      fieldFocalPointY
                    }
                  }
                }
                reverseGirlGirlInfo {
                  entities {
                    ... on GirlInfo {
                      fieldPublicImages {
                        url
                      }
                      fieldPlusAccess
                    }
                  }
                }
              }
            }
            name
            height
            hipsize
            bustsize
            waistsize
            descriptorWeek
            descriptorYear
            descriptorMonth
            description
            birthday {
              value
            }
            city {
              entity {
                entityLabel
              }
            }
            country {
              entity {
                entityLabel
              }
            }
            eyecolor {
              entity {
                entityLabel
              }
            }
            haircolor {
              entity {
                entityLabel
              }
            }
            hometown {
              entity {
                entityLabel
              }
            }
            queryFieldTags {
              entities {
                entityId
                entityLabel
              }
            }
            fieldCategory {
              entity {
                name
              }
            }
            release
            queryMainImages {
              entities {
                ... on MediaImage {
                  fieldMediaImage {
                    url
                  }
                  fieldFocalPointX
                  fieldFocalPointY
                }
              }
            }
            queryGalleries(
              filter: { conditions: { field: "status", value: "1" } }
            ) {
              entities {
                ... on MediaGallery {
                  fieldCredit
                  fieldPublishDate {
                    value
                  }
                  name
                  fieldImage {
                    entity {
                      ... on MediaImage {
                        fieldMediaImage {
                          url
                        }
                        fieldFocalPointX
                        fieldFocalPointY
                      }
                    }
                  }
                  fieldMediaSlideshow {
                    entity {
                      ... on MediaImage {
                        fieldMediaImage {
                          url
                        }
                        fieldFocalPointX
                        fieldFocalPointY
                        mid
                      }
                    }
                  }
                  fieldVideos {
                    entity {
                      ... on MediaNexxVideo {
                        mid
                        fieldNexxId
                        fieldPreviewImage {
                          entity {
                            ... on MediaImage {
                              fieldMediaImage {
                                url
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
            fieldCity {
              entity {
                entityLabel
              }
            }
            fieldCountry {
              entity {
                entityLabel
              }
            }
          }
        }
      }
    }
  }
`;
