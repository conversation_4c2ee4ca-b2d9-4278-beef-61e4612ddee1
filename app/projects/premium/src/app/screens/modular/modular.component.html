@let screenSize = "md" | responsive | async;
@let screenIsMobile = screenSize === false;

<ng-template #grid let-data="data" let-layout="layout" let-title="title" let-button="button" let-isButtonVisible="isButtonVisible">
  @let numberOfTiles = (layout.top === "hero" ? 1 : layout.top || 0) + (layout.bottom || 0);
  <div>
    @if (screenIsMobile) {
      <app-card-carousel class="flex md:!hidden" [title]="title">
        @for (spec of data; let index = $index; track spec) {
          @if (index < numberOfTiles) {
            <ng-template
              cardCarouselSlide
              [isAAContent]="
            spec.reverseGalleriesGirlInfo?.entities[0]?.fieldPlusAccess !== true
          "
              [favoriteType]="'girl-infos'"
              [favoriteId]="
            spec?.reverseGalleriesGirlInfo?.entities[0].id ||
            spec?.reverseGalleriesGirlInfo?.entities[0].entityId
          "
              [image]="
            spec.reverseGalleriesGirlInfo?.entities[0]?.fieldPublicImages?.at(0)
              ?.url || spec.fieldImage?.entity?.fieldMediaImage?.url
          "
              [title]="
            spec.reverseGalleriesGirlInfo?.entities[0].fieldCategory?.entity
              .name
          "
              [text]="
            spec.reverseGalleriesGirlInfo?.entities[0]?.name ||
            spec.reverseGalleriesGirlInfo?.entities[0]?.entityLabel
          "
              [month]="spec.reverseGalleriesGirlInfo?.entities[0].descriptorMonth"
              [year]="spec.reverseGalleriesGirlInfo?.entities[0].descriptorYear"
              [link]="[
            '/girl',
            spec.reverseGalleriesGirlInfo?.entities[0].girl?.targetId ||
              spec.reverseGalleriesGirlInfo?.entities[0]?.queryGirl
                ?.entities?.[0]?.id,
            spec.reverseGalleriesGirlInfo?.entities[0].id ||
              spec.reverseGalleriesGirlInfo?.entities[0].entityId,
          ]"
            >
            </ng-template>
          }
        }
      </app-card-carousel>
    } @else {
      @let gridCols = (layout.top === 3 && layout.bottom === 4) || (layout.top === 4 && layout.top === 3)
        ? 12
        : (layout.bottom === 4 || layout.top === 4) ? 4 : (layout.bottom === 3 || layout.top === 3)
          ? 3
          : (layout.bottom === 2 || layout.top === 2) ? 2 : 1;
      @if (title) {
        <h2 class="mb-6 md:mb-10 py-4 w-full text-center">
          {{ title }}
        </h2>
      }
      <div
        class="grid gap-6"
        [class]="'grid-cols-' + gridCols"
      >
        @for (gallery of data; let index = $index; track index) {
          @if (index < numberOfTiles) {
            <a
              [routerLink]="[
              '/girl',
              gallery.reverseGalleriesGirlInfo?.entities[0]?.girl?.targetId ||
                gallery.reverseGalleriesGirlInfo?.entities[0]?.queryGirl
                  ?.entities?.[0]?.id,
              gallery.reverseGalleriesGirlInfo?.entities[0].id ||
                gallery.reverseGalleriesGirlInfo?.entities[0].entityId,
            ]"
              class="flex h-full w-full"
              [class.w-full]="index === 0 && layout.top === 'hero'"
              [class.col-span-full]="index === 0 && layout.top === 'hero'"
              [class.col-span-3]="gridCols === 12 && layout.bottom === 4 && index >= 3"
              [class.col-span-4]="gridCols === 12 && layout.top === 3 && index < 3"
            >
              <app-gallery-card
                [isAAContent]="
                gallery.reverseGalleriesGirlInfo?.entities[0]
                  ?.fieldPlusAccess !== true
              "
                [favoriteType]="'girl-infos'"
                [favoriteId]="
                gallery.reverseGalleriesGirlInfo?.entities[0].id ||
                gallery.reverseGalleriesGirlInfo?.entities[0].entityId
              "
                [paywallImages]="
                gallery.reverseGalleriesGirlInfo?.entities[0]
                  ?.fieldPublicImagesLow ||
                gallery.reverseGalleriesGirlInfo?.entities[0]?.fieldPublicImages
              "
                [image]="
                gallery.reverseGalleriesGirlInfo?.entities[0]?.fieldPublicImages?.at(
                  0
                )?.url || gallery.fieldImage?.entity?.fieldMediaImage?.url
              "
                [title]="
                gallery.reverseGalleriesGirlInfo?.entities[0].fieldCategory
                  ?.entity?.name
              "
                [month]="
                gallery.reverseGalleriesGirlInfo?.entities[0].descriptorMonth
              "
                [year]="
                gallery.reverseGalleriesGirlInfo?.entities[0].descriptorYear
              "
                [focalPoint]="
                {
                  x:
                    gallery.reverseGalleriesGirlInfo?.entities[0]
                      ?.fieldMainFocalPointX || 50,
                  y:
                    gallery.reverseGalleriesGirlInfo?.entities[0]
                      ?.fieldMainFocalPointY || 50,
                } || gallery.focalPoint
              "
                [name]="
                gallery.reverseGalleriesGirlInfo?.entities[0]?.name ||
                gallery.reverseGalleriesGirlInfo?.entities[0]?.entityLabel
              "
                [imageRatio]="index === 0 && layout.top === 'hero' ? 2 : 1"
                [overwriteImageRatio]="
                index === 0 && layout.top === 'hero' ? '2 / 1' : null
              "
                class="w-full"
                [autoSize]="false"
              >
              </app-gallery-card>
            </a>
          }
        }
      </div>
    }
    @if (isButtonVisible && button?.url && button?.text) {
      <div class="flex px-3 md:px-0 mt-4 md:mt-10 justify-center">
        <a
          [routerLink]="button.url"
          class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
        >
          {{ button.text }}
        </a>
      </div>
    }
  </div>
</ng-template>

<div class="flex flex-col gap-16 py-6 lg:gap-20 lg:py-20">
  @if ($modules | async; as modules) {
    @for (module of modules; track module) {
      @if (module.entity.entityBundle === 'category_module' && module.categories) {
        <div>
          @if (module.title) {
            <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ module.title }}</h2>
          }
          <div class="grid gap-3 lg:gap-6 grid-cols-1 lg:grid-cols-4">
            @for (cat of module.categories; track cat.id) {
              <a
                [routerLink]="cat.link"
                class="flex h-full w-full"
                style="aspect-ratio: 1.5 / 1"
              >
                <app-category-tile
                  [image]="cat.image"
                  [tileTitle]="cat.tileTitle"
                  [focalPoint]="cat.focalPoint"
                ></app-category-tile>
              </a>
            }
          </div>
        </div>
      }
      @if (module.entity.entityBundle === 'e_paper_archiv_module' && module.magazines) {
        <div>
          @if (module.title) {
            <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ module.title }}</h2>
          }
          <div
            class="grid gap-6 md:gap-8 grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-6"
          >
            @for (mag of module.magazines; track mag.url) {
              <a [href]="mag.url" target="_blank" class="flex h-full w-full">
                <app-article-preview
                  class="rounded-lg overflow-hidden"
                  style="aspect-ratio: 1 / 1.27"
                  [image]="mag.field_media_image || mag.field_archiv_image"
                ></app-article-preview>
              </a>
            }
          </div>
          @if (module.isButtonVisible && module.button.url && module.button.text) {
            <div class="flex px-3 md:px-0 mt-6 md:mt-10 justify-center">
              <a
                class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
                [href]="module.button.url"
                target="_blank"
              >
                {{ module.button.text }}
              </a>
            </div>
          }
        </div>
      }
      @if (module.entity.entityBundle === 'favorites_module') {
        <favorites-module
          [title]="module.title"
          [isGirlsVisible]="module.isGirlsVisible"
          [isGirlInfosVisible]="module.isGirlInfosVisible"
          [isVideosVisible]="module.isVideosVisible"
          [pageSize]="module.pageSize"
        />
      }
      @if (module.entity.entityBundle === 'feature_module') {
        <ng-container
          *ngTemplateOutlet="
            grid;
            context: {
              title: module.title,
              isButtonVisible: module.isButtonVisible,
              button: {
                url: module.button.url,
                text: module.button.text,
              },
              data: module.girls,
              layout: module.gridLayout,
            }
          "
        >
        </ng-container>
      }
      @if (module.entity.entityBundle === 'feed_module') {
        <feed-module [title]="module.title"/>
      }
      @if (module.entity.entityBundle === 'girl_des_tages_module' && module.girlsOfTheDay) {
        <ng-container
          *ngTemplateOutlet="
          grid;
          context: {
            title: module.title,
            isButtonVisible: module.isButtonVisible,
            button: {
                url: module.button.url,
                text: module.button.text,
              },
            data: module.girlsOfTheDay,
            layout: module.gridLayout,
          }
      "
        >
        </ng-container>
      }
      @if (module.entity.entityBundle === 'search_module') {
        <div class="py-20">
          @if (module.title) {
            <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ module.title }}</h2>
          }
          <div class="flex flex-col items-center relative mx-auto justify-center">
            <div class="w-full xl:w-1/2 relative z-30">
              <app-search-input
                (triggerSearch)="goToSearch($event)"
                [autofocus]="false"
                [externalMode]="true"
                placeholder="Suche"
              ></app-search-input>
            </div>
          </div>
        </div>
      }
      @if (module.entity.entityBundle === 'video_module') {
        @let maxNumberOfTiles = module.maxNumberOfTiles ?? 4;
        <div>
          @if (screenIsMobile) {
            <app-card-carousel
              class="flex md:!hidden"
              [title]="module.title"
            >
              @for (video of module.videos; track video; let index = $index) {
                @if (index < maxNumberOfTiles) {
                  <ng-template
                    cardCarouselSlide
                    [isAAContent]="getIsVideoAAContent(video)"
                    [favoriteType]="'video'"
                    [favoriteId]="video?.mid"
                    [link]="getVideoRouterLink(video)"
                    [title]="
                  video?.reverseFieldVideosMedia?.entities?.at(0)
                    ?.reverseGalleriesGirlInfo?.entities[0]?.fieldCategory?.entity
                    ?.name
                "
                    [text]="video.entityLabel"
                    [image]="video.fieldPreviewImage.entity?.fieldMediaImage?.url"
                    [nexxID]="video.fieldNexxId"
                  >
                  </ng-template>
                }
              }
            </app-card-carousel>
          } @else {
            @if (module.title) {
              <h2 class="mb-6 md:mb-10 py-4 w-full text-center">
                {{ module.title }}
              </h2>
            }
            <div class="grid gap-6 grid-cols-4 video-list">
              @for (video of module.videos; let index = $index; track index) {
                @if (index < maxNumberOfTiles) {
                  <a
                    [routerLink]="getVideoRouterLink(video)"
                    class="flex h-full w-full"
                  >
                    <app-gallery-card
                      [isAAContent]="getIsVideoAAContent(video)"
                      [favoriteType]="'video'"
                      [favoriteId]="video?.mid"
                      [title]="
                    video?.reverseFieldVideosMedia?.entities?.at(0)
                      ?.reverseGalleriesGirlInfo?.entities[0]?.fieldCategory?.entity
                      ?.name
                  "
                      [image]="video.fieldPreviewImage.entity?.fieldMediaImage?.url"
                      [name]="video.entityLabel"
                      [nexxID]="video.fieldNexxId"
                    >
                    </app-gallery-card>
                  </a>
                }
              }
            </div>
          }

          <div class="flex px-3 md:px-0 mt-6 md:mt-10 justify-center">
            <div
              routerLink="/videos"
              class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
            >
              Mehr Videos
            </div>
          </div>
        </div>
      }
    }
  }
</div>
