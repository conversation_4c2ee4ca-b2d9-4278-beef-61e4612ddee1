import { Component, Input, OnInit } from '@angular/core';
import { SsrCookieService as CookieService } from 'ngx-cookie-service-ssr';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { ModularService } from '../../services/modular/modular.service';
import { AccountService, IAccount } from '../../services/account.service';
import { IVideo } from '../videos/definitions/models';

import {
  ArticlePreviewComponent,
  CardCarouselComponent,
  CardCarouselSlideDirective,
  CategoryTile,
  GalleryCardComponent,
} from '../../shared';
import { AsyncPipe, NgTemplateOutlet } from '@angular/common';
import { ResponsivePipe } from '@pb/ui';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SearchInputComponent } from '../../components/search-input/search-input.component';
import { FavoritesModuleComponent } from '../../shared/components/modular/favorites-module/favorites-module.component';
import { FeedModuleComponent } from '../../shared/components/modular/feed-module/feed-module.component';
import { Meta, Title } from '@angular/platform-browser';

declare var upScore: any;

@Component({
  selector: 'modular',
  templateUrl: './modular.component.html',
  styleUrls: ['./modular.component.css'],
  imports: [
    NgTemplateOutlet,
    AsyncPipe,
    CardCarouselComponent,
    ResponsivePipe,
    CardCarouselSlideDirective,
    RouterLink,
    GalleryCardComponent,
    ArticlePreviewComponent,
    CategoryTile,
    ReactiveFormsModule,
    SearchInputComponent,
    FormsModule,
    FavoritesModuleComponent,
    FeedModuleComponent,
  ],
})
export class ModularComponent implements OnInit {
  @Input() modularUrl?: string;

  $user: Observable<IAccount>;
  $modules: Observable<any>;
  $modularUrl: string;

  private convertUrlToTitle(url: string): string {
    return url
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  constructor(
    private route: ActivatedRoute,
    public readonly accountService: AccountService,
    private cookieService: CookieService,
    private router: Router,
    private modularService: ModularService,
    private titleService: Title,
    private meta: Meta,
  ) {}

  ngOnInit(): void {
    this.$user = this.accountService.Account;

    this.$modularUrl =
      this.modularUrl || this.route.snapshot.paramMap.get('modular_url');

    if (this.$modularUrl !== 'home') {
      this.meta.removeTag(`name='og:site_name'`);
      this.meta.removeTag(`name='og:type'`);
      this.meta.removeTag(`name='og:title'`);
      this.meta.removeTag(`name='og:description'`);
      this.meta.removeTag(`name='og:locale'`);
      this.meta.removeTag(`name='og:url'`);
      this.meta.removeTag(`name='og:image'`);
      this.meta.removeTag(`name='article:published_time'`);
      this.meta.removeTag(`name='profile:first_name'`);
      this.meta.removeTag(`name='profile:last_name'`);
      this.meta.removeTag(`name='profile:gender'`);
      this.meta.removeTag(`name='description'`);
      this.meta.removeTag(`name='author'`);
      this.meta.removeTag(`name='meta'`);
    }

    this.$modules = this.modularService
      .getSubscribedModules({ url: this.$modularUrl })
      .pipe(
        tap((response) => {
          if (this.$modularUrl !== 'home') {
            const pageTitle =
              response.title || this.convertUrlToTitle(this.$modularUrl);
            const fullTitle = `${pageTitle} | Playboy ${this.accountService.subscriptionTypeTitle()}`;
            this.titleService.setTitle(fullTitle);

            this.meta.addTag({
              name: 'og:site_name',
              content: 'Playboy All Access',
            });
            this.meta.addTag({ name: 'og:type', content: 'website' });
            this.meta.addTag({ name: 'og:title', content: pageTitle });
            this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
            this.meta.addTag({
              name: 'og:url',
              content: `https://premium.playboy.de/${this.$modularUrl}`,
            });
          }
        }),
        map((response) => {
          return response.modules;
        }),
      );
  }

  goToSearch(term?: string): void {
    this.router?.navigate(['/search'], { queryParams: { query: term || '' } });
  }

  getVideoRouterLink(video: IVideo): any[] {
    const isAAContent =
      video?.reverseFieldVideosMedia?.entities?.at(0)?.reverseGalleriesGirlInfo
        ?.entities[0]?.fieldPlusAccess !== true;
    if (
      isAAContent &&
      this.accountService.subscriptionType() !== 'all-access'
    ) {
      return [
        '/girl',
        video.reverseFieldVideosMedia.entities[0]?.reverseGalleriesGirlInfo
          .entities[0].girl?.targetId,
        video.reverseFieldVideosMedia.entities[0]?.reverseGalleriesGirlInfo
          .entities[0].entityId,
      ];
    }

    return [
      '/girl-info',
      video.reverseFieldVideosMedia.entities[0]?.reverseGalleriesGirlInfo
        .entities[0].entityId,
      video.mid,
    ];
  }

  getIsVideoAAContent(video: IVideo): boolean {
    const isVideoAA =
      video?.reverseFieldVideosMedia?.entities?.at(0)?.reverseGalleriesGirlInfo
        ?.entities[0]?.fieldPlusAccess !== true;
    const isGirlOfTheDay =
      video?.reverseFieldGirlInfosNode?.entities &&
      video.reverseFieldGirlInfosNode.entities.length > 0;
    const isVideoBlockedForPlusUser =
      this.accountService.subscriptionType() === 'plus' && isGirlOfTheDay;
    return isVideoAA || isVideoBlockedForPlusUser;
  }
}
