import { IPreview } from '../../models/preview';
import {
  PublicImageDerivative,
  QueryMainImages,
} from '../../screens/model/screens/model/definitions/models';
import { CountryOption } from '../../utils/country';
import { IFilterSelection } from '@pb/ui/components/filter-dropdown/filter-dropdown.component';

export interface IDeepPreviewModelGirlInfoGallery {
  queryFieldMediaSlideshow?: {
    count: number;
  };
  queryFieldVideos?: {
    count: number;
  };
}

export interface IDeepPreviewModelGirlInfo {
  queryGalleries?: {
    entities: IDeepPreviewModelGirlInfoGallery[];
  };
}

export interface ISimplePreviewModel {
  entityLabel: string;
  entityId: string;
  fieldImage: {
    entity: {
      fieldFocalPointX: number;
      fieldFocalPointY: number;
      fieldMediaImage: {
        url: string;
        width: number;
        height: number;
      };
    };
  };
  fieldPublishDate: {
    value: string;
  };
  reverseGalleriesGirlInfo: {
    entities: [
      {
        fieldPublicImages: any;
        fieldPublicImagesLow: PublicImageDerivative[];
        fieldPlusAccess?: boolean | string;
        fieldMainFocalPointX?: number;
        fieldMainFocalPointY?: number;
        changed: 1632493768;
        haircolor: null;
        height: null;
        hipsize: null;
        descriptorYear: number;
        descriptorMonth: string;
        entityId: 11991;
        entityLabel: string;
        name: ' ';
        release: number;
        fieldCategory: {
          entity?: {
            name: string;
          };
        };
        fieldImageCount?: number;
        fieldVideoCount?: number;
        queryMainImages: QueryMainImages;
        queryGirl: {
          entities: [
            {
              id: number;
              name: string;
              lastname: string;
              firstname: string;
              reverseGirlGirlInfo: {
                count: number;
                entities: IDeepPreviewModelGirlInfo[];
              };
            },
          ];
        };
      },
    ];
  };
}

export interface ICategoryQueryFilters {
  min?: string;
  max?: string;
}

export interface ICategoryQueryVariableFilters {
  hairColor?: string;
  bustSize?: ICategoryQueryFilters;
  release?: ICategoryQueryFilters;
  pageSize: number;
  page: number;
}

export interface ICategoryQueryVariablePage {
  pageSize: number;
  page: number;
}

export interface ICategoryQueryVariableSort {
  sortField: 'FIELD_PUBLISH_DATE_VALUE' | 'TOTALCOUNT' | 'DAYCOUNT';
  sortDirection: 'DESC' | 'ASC';
}

export type CategoryQueryVariables = ICategoryQueryVariableFilters & {
  field_category: string;
} & ICategoryQueryVariablePage;

export interface IGirlInfoServiceData {
  loading: boolean;
  count: number;
  data: IPreview[];
}

export type SortOptions =
  | 'Beliebteste'
  | 'Beliebteste (heute)'
  | 'Älteste'
  | 'Neueste';

export interface ICategoryFilterOptions {
  Oberweite: string;
  Haarfarbe: string[];
  Augenfarbe: string[];
  Veröffentlichung: string;
  Vorliebe: string;
  Land: CountryOption;
  Kategorie?: string[];
}
