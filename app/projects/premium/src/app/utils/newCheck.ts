import moment from 'moment';
import { MomentInput } from 'moment';

export const NEW_TIMESTAMP = moment().subtract(2, 'days');
export const NEW_TIMESTAMP_DATE = NEW_TIMESTAMP.toDate();
export const NEW_TIMESTAMP_DB = NEW_TIMESTAMP_DATE.toISOString()
  .slice(0, 19)
  .replace('T', ' ');

export function IsNew(input: MomentInput): boolean {
  return !!input && Math.abs(moment(input).diff(moment(), 'days')) <= 2;
}

export function IsNewFromReleaseTimestamp(releaseTimestamp: number): boolean {
  console.log('IsNewFromReleaseTimestamp called with:', releaseTimestamp);

  if (!releaseTimestamp) {
    console.log('No timestamp provided, returning false');
    return false;
  }

  const releaseTimestampDate = new Date(releaseTimestamp * 1000);
  const now = new Date();
  const diffInMs = Math.abs(now.getTime() - releaseTimestampDate.getTime());
  const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

  console.log('Release date:', releaseTimestampDate.toISOString());
  console.log('Current date:', now.toISOString());
  console.log('Difference in days:', diffInDays);
  console.log('Is new (≤2 days):', diffInDays <= 2);

  return diffInDays <= 2;
}
